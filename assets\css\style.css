
@font-face {
  font-family: "Geist", sans-serif;;
  src: url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  font-family: "Geist", sans-serif;
}
#main-wrapper {
  height: 100%;
  width: 100%;
}
a {
  text-decoration: none;
}
.container{
    max-width: 1500px !important;
}

/* Top Navbar Styles */
.upper-navbar {
  background-color: #2c3e50;
  color: white;
  padding: 0;
}

.top-navbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 30px;
  position: sticky;
  top: 0;
  z-index: 1000;
}

.top-navbar .logo {
  display: flex;
  align-items: center;
  gap: 10px;
}

.top-navbar .logo img {
  height: 30px;
}

.brand-name {
  font-size: 1.2rem;
  font-weight: 600;
  color: white;
}

.top-navbar .icons {
  display: flex;
  align-items: center;
  gap: 20px;
}

.top-navbar .icons i {
  font-size: 1.2rem;
  color: white;
  cursor: pointer;
  transition: color 0.3s;
}

.top-navbar .icons i:hover {
  color: #3498db;
}

.top-navbar .dropdown-toggle::after {
  display: none;
}

/* Main Navbar Styles */
.lower-navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
  position: sticky;
  top: 58px;
  z-index: 999;
}

.main-navbar {
  padding: 0 30px;
  overflow-x: auto;
  overflow-y: visible;
  white-space: nowrap;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.main-navbar::-webkit-scrollbar {
  height: 0px;
  background: transparent;
}

.main-navbar .navbar-nav {
  display: flex;
  gap: 15px;
  flex-wrap: nowrap;
  min-width: max-content;
  width: max-content;
}

.main-navbar .nav-item {
  flex-shrink: 0;
  position: relative;
}

.main-navbar .nav-link {
  position: relative;
  color: #2c3e50;
  padding: 15px 0;
  transition: color 0.3s;
  white-space: nowrap;
  font-weight: 500;
}

.main-navbar .nav-link i {
  margin-right: 5px;
}

.main-navbar .nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 100%;
  height: 2px;
  background-color: #3498db;
  transition: transform 0.3s ease-in-out;
  transform-origin: center;
}

.main-navbar .nav-link:hover::after,
.main-navbar .nav-link.active::after {
  transform: translateX(-50%) scaleX(1);
}

.main-navbar .nav-link:hover,
.main-navbar .nav-link.active {
  color: #3498db;
}

/* Dropdown Styles */
.main-navbar .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.main-navbar .dropdown-menu {
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  margin-top: 0;
  position: absolute;
  z-index: 1050;
  top: 100%;
  left: 0;
  min-width: 200px;
  background-color: white;
}

.main-navbar .dropdown-item {
  padding: 10px 16px;
  color: #2c3e50;
  transition: all 0.3s;
  text-decoration: none;
  display: block;
  border: none;
  background: none;
}

.main-navbar .dropdown-item i {
  margin-right: 8px;
  width: 16px;
  text-align: center;
}

.main-navbar .dropdown-item:hover {
  background-color: #f8f9fa;
  color: #3498db;
  transform: translateX(5px);
}

.main-navbar .dropdown-item.active {
  background-color: #3498db;
  color: white;
}

/* Hover dropdown effect */
.main-navbar .nav-item.dropdown:hover .dropdown-menu {
  display: block;
}

/* Main Content Styles */
.main-content {
  padding: 30px 0;
  background-color: #f8f9fa;
  min-height: calc(100vh - 116px);
}

.content-wrapper {
  background-color: white;
  border-radius: 10px;
  padding: 30px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.page-title {
  color: #2c3e50;
  margin-bottom: 30px;
  font-weight: 600;
  border-bottom: 2px solid #3498db;
  padding-bottom: 10px;
}

/* Stats Cards */
.stats-cards {
  margin-bottom: 30px;
}

.stat-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 25px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  gap: 20px;
  transition: transform 0.3s, box-shadow 0.3s;
  height: 120px;
}

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-card:nth-child(2) .stat-card {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card:nth-child(3) .stat-card {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stat-card:nth-child(4) .stat-card {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stat-icon {
  font-size: 2.5rem;
  opacity: 0.8;
}

.stat-content h3 {
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.stat-content p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

/* Card Styles */
.card {
  border: none;
  border-radius: 10px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s;
}

.card:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 10px 10px 0 0 !important;
  padding: 15px 20px;
}

.card-header h5 {
  margin: 0;
  color: #2c3e50;
  font-weight: 600;
}

/* Table Styles */
.table {
  margin: 0;
}

.table th {
  background-color: #f8f9fa;
  color: #2c3e50;
  font-weight: 600;
  border-top: none;
  padding: 15px 10px;
}

.table td {
  padding: 15px 10px;
  vertical-align: middle;
}

.table-striped tbody tr:nth-of-type(odd) {
  background-color: rgba(0, 0, 0, 0.02);
}

/* Badge Styles */
.badge {
  font-size: 0.75rem;
  padding: 5px 10px;
}

/* Button Styles */
.btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s;
}

.btn:hover {
  transform: translateY(-1px);
}

.btn-sm {
  padding: 5px 10px;
  font-size: 0.8rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .top-navbar {
    padding: 10px 15px;
  }

  .main-navbar {
    padding: 0 15px;
  }

  .main-content {
    padding: 20px 0;
  }

  .content-wrapper {
    padding: 20px;
  }

  .stat-card {
    margin-bottom: 15px;
  }

  .table-responsive {
    font-size: 0.9rem;
  }
}

/* Utility Classes */
.text-primary {
  color: #3498db !important;
}

.bg-primary {
  background-color: #3498db !important;
}

.border-primary {
  border-color: #3498db !important;
}

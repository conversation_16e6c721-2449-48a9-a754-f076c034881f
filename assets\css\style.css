
@font-face {
  font-family: "Geist", sans-serif;;
  src: url("https://fonts.googleapis.com/css2?family=Geist:wght@100..900&display=swap");
}
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}
html,
body {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  font-family: "Geist", sans-serif;
}
#main-wrapper {
  height: 100%;
  width: 100%;
}
a {
  text-decoration: none;
}
.container{
    max-width: 1500px !important;
}
.upper-navbar, .lower-navbar {
  border-bottom: 1px solid #ddd;
}
.top-navbar {
  background-color: #ffffff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 30px;
  /* border-bottom: 1px solid #ddd; */
  position: sticky;
  top: 0;
  z-index: 1000;
}
.top-navbar img {
  height: 30px;
}
.top-navbar .icons {
  display: flex;
  align-items: center;
  /* justify-content: space-between; */

  gap: 20px;
}
.top-navbar .dropdown-toggle::after {
  display: none;
}
.main-navbar {
  background-color: #ffffff;
  border-bottom: 1px solid #ddd;
  padding: 0 30px;
  position: sticky;
  top: 58px;
  z-index: 999;
  overflow-x: auto;
  overflow-y: visible;
  white-space: nowrap;
  /* Hide scrollbar by default */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}
.main-navbar .nav-link::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%) scaleX(0);
  width: 100%;
  height: 2px;
  background-color: red;
  transition: transform 0.3s ease-in-out;
  transform-origin: center;
}
.main-navbar .nav-link:hover::after,
.main-navbar .nav-link.active::after {
  transform: translateX(-50%) scaleX(1);
}
.main-navbar .nav-link:hover {
  color: red;
}
/* Hide scrollbar for webkit browsers by default */
.main-navbar::-webkit-scrollbar {
  height: 0px;
  background: transparent;
  transition: height 0.3s ease-in-out;
}
/* Show scrollbar on hover for webkit browsers */
.main-navbar:hover::-webkit-scrollbar {
  height: 3px;
}
.main-navbar::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
  transition: background 0.3s ease;
}
.main-navbar::-webkit-scrollbar-thumb:hover {
  background: #555;
}
.main-navbar::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 10px;
}
/* Firefox scrollbar on hover */
.main-navbar:hover {
  scrollbar-width: thin;
  scrollbar-color: #888 #f1f1f1;
}
/* Fix for navbar list overflow */
.main-navbar .navbar-nav {
  display: flex;
  gap: 15px;
  flex-wrap: nowrap;
  min-width: max-content; /* important for overflow */
  width: max-content; /* Ensure it takes full width of content */
}

/* Nav links styles */
.main-navbar .nav-link {
  position: relative;
  color: #000;
  padding: 15px 0;
  transition: color 0.3s;
  white-space: nowrap;
}
/* Additional styles for better horizontal scrolling */
.lower-navbar .container {
  max-width: 100% !important;
  padding: 0;
}
.lower-navbar .row {
  margin: 0;
}
.lower-navbar .col-12 {
  padding: 0;
}
/* Ensure navbar items don't wrap */
.main-navbar .nav-item {
  flex-shrink: 0;
}

/* Dropdown positioning */
.main-navbar .nav-item.dropdown {
  position: relative;
}

/* Product dropdown styles */
.main-navbar .dropdown-toggle::after {
  display: inline-block;
  margin-left: 0.255em;
  vertical-align: 0.255em;
  content: "";
  border-top: 0.3em solid;
  border-right: 0.3em solid transparent;
  border-bottom: 0;
  border-left: 0.3em solid transparent;
}

.main-navbar .dropdown-menu {
  border: 1px solid #ddd;
  border-radius: 4px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-top: 0;
  position: absolute;
  z-index: 1000;
  top: 100%;
  left: 0;
  min-width: 160px;
}

.main-navbar .dropdown-item {
  padding: 8px 16px;
  color: #000;
  transition: background-color 0.3s, color 0.3s;
}

.main-navbar .dropdown-item:hover {
  background-color: #f8f9fa;
  color: red;
}

.main-navbar .dropdown-item.active {
  background-color: red;
  color: white;
}

.main-navbar .dropdown-item.active:hover {
  background-color: #dc3545;
  color: white;
}

/* Ensure dropdown container doesn't clip */
.navbar-wrapper {
  overflow: visible;
}

.lower-navbar {
  overflow: visible;
}

.lower-navbar .container {
  overflow: visible;
}

.lower-navbar .row {
  overflow: visible;
}

.lower-navbar .col-12 {
  overflow: visible;
}
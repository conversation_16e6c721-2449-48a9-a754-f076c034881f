// Initialize Bootstrap dropdowns
document.addEventListener('DOMContentLoaded', function() {
    // Initialize all dropdowns
    var dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
    var dropdownList = dropdownElementList.map(function (dropdownToggleEl) {
        return new bootstrap.Dropdown(dropdownToggleEl);
    });

    // Debug: Log when dropdown is shown
    document.querySelectorAll('.dropdown').forEach(function(dropdown) {
        dropdown.addEventListener('shown.bs.dropdown', function () {
            console.log('Dropdown shown');
        });

        dropdown.addEventListener('hidden.bs.dropdown', function () {
            console.log('Dropdown hidden');
        });
    });
});